package v1alpha2

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:subresource:status
// +kubebuilder:resource:shortName=spb,categories={karmada-io}

// StatefulPodBinding represents a binding of a statefulset workload with specific pod indices distribution
// across member clusters. It is used to store the distribution information of different index pods
// for stateful workloads.
type StatefulPodBinding struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	// Spec represents the desired behavior.
	Spec ResourceBindingSpec `json:"spec"`

	// Status represents the most recently observed status of the StatefulPodBinding.
	// +optional
	Status StatefulPodBindingStatus `json:"status,omitempty"`
}

// StatefulPodBindingStatus represents the overall status of the StatefulPodBinding as well as the referenced workload.
type StatefulPodBindingStatus struct {
	// Conditions contain the different condition statuses.
	// +optional
	Conditions []metav1.Condition `json:"conditions,omitempty"`

	// AggregatedStatus represents status list of the pods running in each member cluster.
	// +optional
	AggregatedStatus []PodAggregatedStatusItem `json:"aggregatedStatus,omitempty"`
}

// PodAggregatedStatusItem represents status of the pods running in a member cluster.
type PodAggregatedStatusItem struct {
	// ClusterName represents the member cluster name which the pods are deployed on.
	// +required
	ClusterName string `json:"clusterName"`

	// PodStatuses represents the status of each pod deployed on this cluster.
	// +optional
	PodStatuses []PodStatus `json:"podStatuses,omitempty"`
}

// PodStatus represents the status of a single pod in the statefulset.
type PodStatus struct {
	// PodIndex represents the index of the pod in the statefulset.
	PodIndex int32 `json:"podIndex"`

	// PodName represents the name of the pod.
	// +optional
	PodName string `json:"podName,omitempty"`

	// Status reflects running status of current pod.
	// +kubebuilder:pruning:PreserveUnknownFields
	// +optional
	Status *runtime.RawExtension `json:"status,omitempty"`

	// Applied represents if the pod is successfully applied on the cluster.
	// +optional
	Applied bool `json:"applied,omitempty"`

	// AppliedMessage is a human readable message indicating details about the applied status.
	// This is usually holds the error message in case of apply failed.
	// +optional
	AppliedMessage string `json:"appliedMessage,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// StatefulPodBindingList contains a list of StatefulPodBinding.
type StatefulPodBindingList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`

	// Items is the list of StatefulPodBinding.
	Items []StatefulPodBinding `json:"items"`
}

package v1alpha1

const (
	// PropagationPolicyUIDLabel is the uid of PropagationPolicy object.
	PropagationPolicyUIDLabel = "propagationpolicy.karmada.io/uid"

	// PropagationPolicyNamespaceAnnotation is added to objects to specify associated PropagationPolicy namespace.
	PropagationPolicyNamespaceAnnotation = "propagationpolicy.karmada.io/namespace"

	// PropagationPolicyNameAnnotation is added to objects to specify associated PropagationPolicy name.
	PropagationPolicyNameAnnotation = "propagationpolicy.karmada.io/name"

	// ClusterPropagationPolicyUIDLabel is the uid of ClusterPropagationPolicy object.
	ClusterPropagationPolicyUIDLabel = "clusterpropagationpolicy.karmada.io/uid"

	// ClusterPropagationPolicyAnnotation is added to objects to specify associated ClusterPropagationPolicy name.
	ClusterPropagationPolicyAnnotation = "clusterpropagationpolicy.karmada.io/name"

	// PropagationPolicyNamespaceLabel is added to objects to specify associated PropagationPolicy namespace.
	PropagationPolicyNamespaceLabel = "propagationpolicy.karmada.io/namespace"

	// PropagationPolicyNameLabel is added to objects to specify associated PropagationPolicy's name.
	PropagationPolicyNameLabel = "propagationpolicy.karmada.io/name"

	// ClusterPropagationPolicyLabel is added to objects to specify associated ClusterPropagationPolicy.
	ClusterPropagationPolicyLabel = "clusterpropagationpolicy.karmada.io/name"

	// NamespaceSkipAutoPropagationLabel is added to namespace objects to indicate if
	// the namespace should be skipped from propagating by the namespace controller.
	// For example, a namespace with the following label will be skipped:
	//   labels:
	//     namespace.karmada.io/skip-auto-propagation: "true"
	//
	// NOTE: If create a ns without this label, then patch it with this label, the ns will not be
	// synced to new member clusters, but old member clusters still have it.
	NamespaceSkipAutoPropagationLabel = "namespace.karmada.io/skip-auto-propagation"

	// AnnotationPodsPropagateCluster is the annotation key used to specify target clusters for pod propagation.
	// The value should be a comma-separated list of cluster names, optionally with weights.
	// Examples:
	//   annotations:
	//     pods.propagate.karmada.io/clusters: "member1,member2"
	//     pods.propagate.karmada.io/clusters: "member1:2,member2:1"
	AnnotationPodsPropagateCluster = "pods.propagate.karmada.io/clusters"

	// AnnotationPodsFixedPlacement is the annotation key used to specify fixed pod placement for StatefulSets.
	// The value should specify which pod indexes should be placed on which clusters.
	// Example:
	//   annotations:
	//     pods.propagate.karmada.io/fixed-placement: "member1:[0,1],member2:[2,3]"
	AnnotationPodsFixedPlacement = "pods.propagate.karmada.io/fixed-placement"

	// LabelPolicyCreatedByAnno is the label key used to mark policies that were created from annotations.
	LabelPolicyCreatedByAnno = "policy.karmada.io/created-by-annotation"

	// LabelPodsPropagatePolicy is the label key used to specify a custom policy name for pod propagation.
	LabelPodsPropagatePolicy = "pods.propagate.karmada.io/policy"
)

const (
	// LabelWorkloadPropagateType  only support pod 分发类型
	LabelWorkloadPropagateType    = "apps.dewu.com/workload-propagate-type"
	LabelWorkloadPropagateTypePod = "pod"

	LabelPodsPropagatePolicy = "apps.dewu.com/pods-propagate-policy"
	LabelPolicyCreatedByAnno = "propagationpolicy.karmada.io/created-by-annotations"

	// Annotation keys for stateful workload propagation

	AnnotationPodsPropagateCluster    = "apps.dewu.com/pods-propagate-clusters"
	AnnotationPodsFixedPlacement      = "apps.dewu.com/pods-fixed-placement"
	AnnotationPropagateUpdateStrategy = "apps.dewu.com/propagate-update-strategy"
)

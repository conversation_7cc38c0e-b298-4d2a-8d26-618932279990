# StatefulPodBinding Controller

调协 StatefulPodBinding, 获取当前 workload 的 pod 列表。
基于当前pod的分布，和分发策略为每个pod 选择一个合适的 member node.
将结果写到 StatefulPodBinding Spec 的 Clusters []PodTargetCluster 中去。

通过 类似于调度器的方式 创建binding 对象的方式给没有分配node 的pod写上nodeName. 格式为 vk-{member-cluster-name}

对于按 Pod 粒度分发，replicaScheduling 策略允许灵活控制每个 Pod 的去向：
1. Placement *policyv1alpha1.Placement 提供了一种基于权重的通用分配方法。当 StatefulSet 伸缩（增加副本）或分发策略中集群权重发生变化时，此策略主要用于决定新增的、或尚未分配成员集群的 Pod应该被分配到哪个集群。它旨在根据权重比例动态平衡新 Pod 在各集群间的分布。
2. 其中 fixedPodPlacement 提供了更细致的控制能力，允许用户将特定的、具有关键序号的 Pod（例如主节点、特定数据分片）精确地部署到指定的成员集群中。此配置具有最高优先级。一旦 Pod 通过 fixedPodPlacement 指定了成员集群，除非此配置自身发生变更，否则控制器不会主动将其迁移到其他集群，即使 weightedPlacement 的权重发生变化



updateStrategy 用于定义当pod 分发策略变更时，pod 的迁移方式：（暂不实现）
1. OnDelete:  控制器不会触发Pod 主动迁移，除非pod发生重建，重建后的pod 会被分发到新的member 集群中去，pod 的迁移可以在 operator 对 pod 滚动升级时，同时重调度归属的member k8s 集群
2. RollingUpdate:  控制器自动对Pod进行迁移（类似于StatefulSet 的滚动升级）对需要迁移的pod 进行重建，已有pod 会被删除，新的pod部署到新的member 集群中去，遵循 maxUnavailable 语义。
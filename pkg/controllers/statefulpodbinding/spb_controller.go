package statefulpodbinding

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/predicate"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	schedulercache "github.com/karmada-io/karmada/pkg/scheduler/cache"
	"github.com/karmada-io/karmada/pkg/scheduler/core"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util"
)

// ControllerName is the controller name that will be used when reporting events.
const ControllerName = "stateful-pod-binding-controller"

// Controller reconciles StatefulPodBinding objects
type Controller struct {
	client.Client
	Scheme                      *runtime.Scheme
	EventRecorder               record.EventRecorder
	RateLimiterOptions          ratelimiterflag.Options
	ClusterClientSetFunc        func(clusterName string, client client.Client) (*util.ClusterClient, error)
	ClusterDynamicClientSetFunc func(clusterName string, client client.Client) (*util.DynamicClusterClient, error)

	Algorithm      core.ScheduleAlgorithm
	schedulerCache schedulercache.Cache
}

func NewController(mgr controllerruntime.Manager, opts ...Option) (*Controller, error) {
	c := &Controller{
		Client:                      mgr.GetClient(),
		Scheme:                      mgr.GetScheme(),
		EventRecorder:               mgr.GetEventRecorderFor(ControllerName),
		RateLimiterOptions:          ratelimiterflag.Options{},
		ClusterClientSetFunc:        util.NewClusterClientSet,
		ClusterDynamicClientSetFunc: util.NewClusterDynamicClientSet,
	}

	for _, opt := range opts {
		opt(c)
	}

	return c, nil
}

// Reconcile performs a full reconciliation for the object referred to by the Request.
func (c *Controller) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error) {
	klog.V(4).Infof("Reconciling StatefulPodBinding %s", req.NamespacedName.String())

	spb := &workv1alpha2.StatefulPodBinding{}
	if err := c.Client.Get(ctx, req.NamespacedName, spb); err != nil {
		// The resource may no longer exist, in which case we stop processing.
		if apierrors.IsNotFound(err) {
			return controllerruntime.Result{}, nil
		}
		return controllerruntime.Result{Requeue: true}, err
	}

	// The object is being deleted
	if !spb.DeletionTimestamp.IsZero() {
		return controllerruntime.Result{}, nil
	}

	return c.syncStatefulPodBinding(ctx, spb)
}

// syncStatefulPodBinding syncs the StatefulPodBinding
func (c *Controller) syncStatefulPodBinding(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) (controllerruntime.Result, error) {
	klog.V(2).Infof("Syncing StatefulPodBinding %s/%s", spb.Namespace, spb.Name)

	// Get the referenced workload (StatefulSet)
	workload, err := c.getWorkload(ctx, spb)
	if err != nil {
		klog.Errorf("Failed to get workload for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Get current pod distribution across clusters
	currentPodDistribution, err := c.getCurrentPodDistribution(ctx, spb, workload)
	if err != nil {
		klog.Errorf("Failed to get current pod distribution for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Get available clusters
	clusters, err := c.getAvailableClusters(ctx)
	if err != nil {
		klog.Errorf("Failed to get available clusters: %v", err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Schedule pods to clusters based on placement strategy
	scheduledClusters, err := c.schedulePods(ctx, spb, workload, currentPodDistribution, clusters)
	if err != nil {
		klog.Errorf("Failed to schedule pods for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Update StatefulPodBinding spec with scheduled clusters
	return c.updateStatefulPodBinding(ctx, spb, scheduledClusters)
}

func (c *Controller) assignReplicas(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) ([]workv1alpha2., error) {
	c.Algorithm.Schedule(ctx, spb.Spec, )
}

// getWorkload gets the referenced workload (StatefulSet) from the StatefulPodBinding
func (c *Controller) getWorkload(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) (*appsv1.StatefulSet, error) {
	workloadRef := spb.Spec.WorkloadRef

	// Currently only support StatefulSet
	if workloadRef.Kind != "StatefulSet" {
		return nil, fmt.Errorf("unsupported workload kind: %s", workloadRef.Kind)
	}

	statefulSet := &appsv1.StatefulSet{}
	namespacedName := types.NamespacedName{
		Namespace: workloadRef.Namespace,
		Name:      workloadRef.Name,
	}

	if err := c.Client.Get(ctx, namespacedName, statefulSet); err != nil {
		return nil, fmt.Errorf("failed to get StatefulSet %s/%s: %w", workloadRef.Namespace, workloadRef.Name, err)
	}

	return statefulSet, nil
}

// getCurrentPodDistribution gets the current pod distribution across member clusters
func (c *Controller) getCurrentPodDistribution(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, workload *appsv1.StatefulSet) (map[string][]int32, error) {
	distribution := make(map[string][]int32)

	// Get all member clusters
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}

	// For each cluster, get the pods of the StatefulSet
	for _, cluster := range clusterList.Items {
		if cluster.Spec.SyncMode == clusterv1alpha1.Pull {
			// Skip pull mode clusters as we can't directly access them
			continue
		}

		podIndices, err := c.getPodsInCluster(ctx, cluster.Name, workload)
		if err != nil {
			klog.Warningf("Failed to get pods in cluster %s: %v", cluster.Name, err)
			continue
		}

		if len(podIndices) > 0 {
			distribution[cluster.Name] = podIndices
		}
	}

	return distribution, nil
}

// getPodsInCluster gets the pod indices of the StatefulSet in a specific cluster
func (c *Controller) getPodsInCluster(ctx context.Context, clusterName string, workload *appsv1.StatefulSet) ([]int32, error) {
	// Get cluster client
	clusterClient, err := c.ClusterClientSetFunc(clusterName, c.Client)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster client for %s: %w", clusterName, err)
	}

	if clusterClient.KubeClient == nil {
		return nil, fmt.Errorf("cluster %s is not ready", clusterName)
	}

	// List pods with the StatefulSet's selector
	selector, err := metav1.LabelSelectorAsSelector(workload.Spec.Selector)
	if err != nil {
		return nil, fmt.Errorf("failed to convert label selector: %w", err)
	}

	podList, err := clusterClient.KubeClient.CoreV1().Pods(workload.Namespace).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods in cluster %s: %w", clusterName, err)
	}

	var podIndices []int32
	for _, pod := range podList.Items {
		// Extract pod index from pod name (e.g., "my-sts-0" -> 0)
		if index, err := getPodIndexFromName(pod.Name, workload.Name); err == nil {
			podIndices = append(podIndices, int32(index))
		}
	}

	sort.Slice(podIndices, func(i, j int) bool {
		return podIndices[i] < podIndices[j]
	})

	return podIndices, nil
}

// getAvailableClusters gets all available member clusters
func (c *Controller) getAvailableClusters(ctx context.Context) ([]*clusterv1alpha1.Cluster, error) {
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}

	var availableClusters []*clusterv1alpha1.Cluster
	for i := range clusterList.Items {
		cluster := &clusterList.Items[i]
		// Only include ready clusters
		if isClusterReady(&cluster.Status) {
			availableClusters = append(availableClusters, cluster)
		}
	}

	return availableClusters, nil
}

// schedulePods schedules pods to clusters based on placement strategy
func (c *Controller) schedulePods(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, workload *appsv1.StatefulSet, currentDistribution map[string][]int32, clusters []*clusterv1alpha1.Cluster) ([]workv1alpha2.PodTargetCluster, error) {
	replicas := spb.Spec.Replicas
	if replicas == 0 && workload.Spec.Replicas != nil {
		replicas = *workload.Spec.Replicas
	}

	// Initialize result with current distribution
	clusterPodMap := make(map[string][]int32)
	for clusterName, podIndices := range currentDistribution {
		clusterPodMap[clusterName] = append([]int32{}, podIndices...)
	}

	// Apply placement strategy
	if spb.Spec.Placement != nil {
		if err := c.applyPlacementStrategy(spb.Spec.Placement, replicas, clusterPodMap, clusters); err != nil {
			return nil, fmt.Errorf("failed to apply placement strategy: %w", err)
		}
	} else {
		// Default strategy: distribute evenly across all clusters
		if err := c.applyDefaultPlacementStrategy(replicas, clusterPodMap, clusters); err != nil {
			return nil, fmt.Errorf("failed to apply default placement strategy: %w", err)
		}
	}

	// Convert to PodTargetCluster format
	var result []workv1alpha2.PodTargetCluster
	for clusterName, podIndices := range clusterPodMap {
		if len(podIndices) > 0 {
			sort.Slice(podIndices, func(i, j int) bool {
				return podIndices[i] < podIndices[j]
			})
			result = append(result, workv1alpha2.PodTargetCluster{
				Name:       clusterName,
				PodIndices: podIndices,
			})
		}
	}

	// Sort clusters by name for consistent output
	sort.Slice(result, func(i, j int) bool {
		return result[i].Name < result[j].Name
	})

	return result, nil
}

// applyPlacementStrategy applies the placement strategy to schedule pods
func (c *Controller) applyPlacementStrategy(placement *policyv1alpha1.Placement, replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	// Handle fixed pod placement first (highest priority)
	if placement.ReplicaScheduling != nil && len(placement.ReplicaScheduling.StatefulFixedPodPlacement) > 0 {
		for _, fixedPlacement := range placement.ReplicaScheduling.StatefulFixedPodPlacement {
			for _, podIndex := range fixedPlacement.PodIndexes {
				if podIndex >= replicas {
					continue // Skip invalid pod indices
				}

				// Remove pod from current clusters
				c.removePodFromClusters(podIndex, clusterPodMap)

				// Add pod to target cluster
				if clusterPodMap[fixedPlacement.ClusterName] == nil {
					clusterPodMap[fixedPlacement.ClusterName] = []int32{}
				}
				clusterPodMap[fixedPlacement.ClusterName] = append(clusterPodMap[fixedPlacement.ClusterName], podIndex)
			}
		}
	}

	// Handle weighted placement for remaining pods
	if placement.ReplicaScheduling != nil && placement.ReplicaScheduling.WeightPreference != nil {
		return c.applyWeightedPlacement(placement.ReplicaScheduling.WeightPreference, replicas, clusterPodMap, clusters)
	}

	return nil
}

// removePodFromClusters removes a pod from all clusters
func (c *Controller) removePodFromClusters(podIndex int32, clusterPodMap map[string][]int32) {
	for clusterName, podIndices := range clusterPodMap {
		for i, idx := range podIndices {
			if idx == podIndex {
				// Remove pod from this cluster
				clusterPodMap[clusterName] = append(podIndices[:i], podIndices[i+1:]...)
				break
			}
		}
	}
}

// applyWeightedPlacement applies weighted placement strategy
func (c *Controller) applyWeightedPlacement(weightPreference *policyv1alpha1.ClusterPreferences, replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	// Get all assigned pods
	assignedPods := make(map[int32]bool)
	for _, podIndices := range clusterPodMap {
		for _, podIndex := range podIndices {
			assignedPods[podIndex] = true
		}
	}

	// Find unassigned pods
	var unassignedPods []int32
	for i := int32(0); i < replicas; i++ {
		if !assignedPods[i] {
			unassignedPods = append(unassignedPods, i)
		}
	}

	if len(unassignedPods) == 0 {
		return nil // All pods are already assigned
	}

	// Calculate cluster weights
	clusterWeights := c.calculateClusterWeights(weightPreference, clusters)
	if len(clusterWeights) == 0 {
		return fmt.Errorf("no clusters available for weighted placement")
	}

	// Distribute unassigned pods based on weights
	return c.distributePodsBasedOnWeights(unassignedPods, clusterWeights, clusterPodMap)
}

// applyDefaultPlacementStrategy applies default even distribution strategy
func (c *Controller) applyDefaultPlacementStrategy(replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	if len(clusters) == 0 {
		return fmt.Errorf("no clusters available")
	}

	// Clear current distribution
	for clusterName := range clusterPodMap {
		clusterPodMap[clusterName] = []int32{}
	}

	// Distribute pods evenly across clusters
	clusterNames := make([]string, len(clusters))
	for i, cluster := range clusters {
		clusterNames[i] = cluster.Name
	}
	sort.Strings(clusterNames) // Ensure consistent ordering

	for i := int32(0); i < replicas; i++ {
		clusterName := clusterNames[i%int32(len(clusterNames))]
		if clusterPodMap[clusterName] == nil {
			clusterPodMap[clusterName] = []int32{}
		}
		clusterPodMap[clusterName] = append(clusterPodMap[clusterName], i)
	}

	return nil
}

// calculateClusterWeights calculates cluster weights based on weight preference
func (c *Controller) calculateClusterWeights(weightPreference *policyv1alpha1.ClusterPreferences, clusters []*clusterv1alpha1.Cluster) map[string]int64 {
	clusterWeights := make(map[string]int64)

	// Initialize all clusters with weight 1 (default)
	for _, cluster := range clusters {
		clusterWeights[cluster.Name] = 1
	}

	// Apply static weights if specified
	if weightPreference.StaticWeightList != nil {
		for _, weight := range weightPreference.StaticWeightList {
			if weight.Weight > 0 {
				clusterWeights[weight.TargetCluster.Name] = weight.Weight
			}
		}
	}

	return clusterWeights
}

// distributePodsBasedOnWeights distributes pods based on cluster weights
func (c *Controller) distributePodsBasedOnWeights(unassignedPods []int32, clusterWeights map[string]int64, clusterPodMap map[string][]int32) error {
	if len(clusterWeights) == 0 {
		return fmt.Errorf("no clusters with weights available")
	}

	// Calculate total weight
	var totalWeight int64
	for _, weight := range clusterWeights {
		totalWeight += weight
	}

	// Sort cluster names for consistent ordering
	var clusterNames []string
	for clusterName := range clusterWeights {
		clusterNames = append(clusterNames, clusterName)
	}
	sort.Strings(clusterNames)

	// Distribute pods proportionally
	podIndex := 0
	for _, clusterName := range clusterNames {
		weight := clusterWeights[clusterName]
		podCount := int(int64(len(unassignedPods)) * weight / totalWeight)

		// Assign pods to this cluster
		for i := 0; i < podCount && podIndex < len(unassignedPods); i++ {
			if clusterPodMap[clusterName] == nil {
				clusterPodMap[clusterName] = []int32{}
			}
			clusterPodMap[clusterName] = append(clusterPodMap[clusterName], unassignedPods[podIndex])
			podIndex++
		}
	}

	// Assign remaining pods to clusters with highest weights
	for podIndex < len(unassignedPods) {
		// Find cluster with highest weight that has capacity
		var bestCluster string
		var bestWeight int64
		for _, clusterName := range clusterNames {
			if clusterWeights[clusterName] > bestWeight {
				bestCluster = clusterName
				bestWeight = clusterWeights[clusterName]
			}
		}

		if bestCluster != "" {
			if clusterPodMap[bestCluster] == nil {
				clusterPodMap[bestCluster] = []int32{}
			}
			clusterPodMap[bestCluster] = append(clusterPodMap[bestCluster], unassignedPods[podIndex])
			podIndex++
		} else {
			break
		}
	}

	return nil
}

// updateStatefulPodBinding updates the StatefulPodBinding with scheduled clusters
func (c *Controller) updateStatefulPodBinding(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, scheduledClusters []workv1alpha2.PodTargetCluster) (controllerruntime.Result, error) {
	// Check if update is needed
	if c.clustersEqual(spb.Spec.Clusters, scheduledClusters) {
		klog.V(4).Infof("StatefulPodBinding %s/%s clusters unchanged, skipping update", spb.Namespace, spb.Name)
		return controllerruntime.Result{}, nil
	}

	// Update the spec
	spb.Spec.Clusters = scheduledClusters

	if err := c.Client.Update(ctx, spb); err != nil {
		klog.Errorf("Failed to update StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	klog.V(2).Infof("Successfully updated StatefulPodBinding %s/%s with %d clusters", spb.Namespace, spb.Name, len(scheduledClusters))
	return controllerruntime.Result{}, nil
}

// clustersEqual checks if two cluster lists are equal
func (c *Controller) clustersEqual(current, new []workv1alpha2.PodTargetCluster) bool {
	if len(current) != len(new) {
		return false
	}

	// Create maps for comparison
	currentMap := make(map[string][]int32)
	for _, cluster := range current {
		currentMap[cluster.Name] = cluster.PodIndices
	}

	newMap := make(map[string][]int32)
	for _, cluster := range new {
		newMap[cluster.Name] = cluster.PodIndices
	}

	// Compare maps
	for clusterName, currentIndices := range currentMap {
		newIndices, exists := newMap[clusterName]
		if !exists || !c.slicesEqual(currentIndices, newIndices) {
			return false
		}
	}

	for clusterName := range newMap {
		if _, exists := currentMap[clusterName]; !exists {
			return false
		}
	}

	return true
}

// slicesEqual checks if two int32 slices are equal
func (c *Controller) slicesEqual(a, b []int32) bool {
	if len(a) != len(b) {
		return false
	}

	// Sort both slices for comparison
	sortedA := make([]int32, len(a))
	sortedB := make([]int32, len(b))
	copy(sortedA, a)
	copy(sortedB, b)

	sort.Slice(sortedA, func(i, j int) bool { return sortedA[i] < sortedA[j] })
	sort.Slice(sortedB, func(i, j int) bool { return sortedB[i] < sortedB[j] })

	for i := range sortedA {
		if sortedA[i] != sortedB[i] {
			return false
		}
	}

	return true
}

// SetupWithManager creates a controller and register to controller manager.
func (c *Controller) SetupWithManager(mgr controllerruntime.Manager) error {
	return controllerruntime.NewControllerManagedBy(mgr).
		For(&workv1alpha2.StatefulPodBinding{}).
		WithEventFilter(predicate.GenerationChangedPredicate{}).
		Watches(&appsv1.StatefulSet{}, handler.EnqueueRequestsFromMapFunc(c.newStatefulSetMapFunc())).
		Watches(&clusterv1alpha1.Cluster{}, handler.EnqueueRequestsFromMapFunc(c.newClusterMapFunc())).
		WithOptions(controller.Options{RateLimiter: ratelimiterflag.DefaultControllerRateLimiter(c.RateLimiterOptions)}).
		Complete(c)
}

// newStatefulSetMapFunc returns a map function for StatefulSet events
func (c *Controller) newStatefulSetMapFunc() handler.MapFunc {
	return func(ctx context.Context, obj client.Object) []controllerruntime.Request {
		statefulSet, ok := obj.(*appsv1.StatefulSet)
		if !ok {
			return nil
		}

		// Find StatefulPodBindings that reference this StatefulSet
		spbList := &workv1alpha2.StatefulPodBindingList{}
		if err := c.Client.List(ctx, spbList); err != nil {
			klog.Errorf("Failed to list StatefulPodBindings: %v", err)
			return nil
		}

		var requests []controllerruntime.Request
		for _, spb := range spbList.Items {
			if spb.Spec.WorkloadRef.Kind == "StatefulSet" &&
				spb.Spec.WorkloadRef.Namespace == statefulSet.Namespace &&
				spb.Spec.WorkloadRef.Name == statefulSet.Name {
				requests = append(requests, controllerruntime.Request{
					NamespacedName: types.NamespacedName{
						Namespace: spb.Namespace,
						Name:      spb.Name,
					},
				})
			}
		}

		return requests
	}
}

// newClusterMapFunc returns a map function for Cluster events
func (c *Controller) newClusterMapFunc() handler.MapFunc {
	return func(ctx context.Context, obj client.Object) []controllerruntime.Request {
		// When cluster status changes, we need to re-evaluate all StatefulPodBindings
		spbList := &workv1alpha2.StatefulPodBindingList{}
		if err := c.Client.List(ctx, spbList); err != nil {
			klog.Errorf("Failed to list StatefulPodBindings: %v", err)
			return nil
		}

		var requests []controllerruntime.Request
		for _, spb := range spbList.Items {
			requests = append(requests, controllerruntime.Request{
				NamespacedName: types.NamespacedName{
					Namespace: spb.Namespace,
					Name:      spb.Name,
				},
			})
		}

		return requests
	}
}

// getPodIndexFromName extracts the pod index from StatefulSet pod name
// For example: "my-sts-0" -> 0, "my-sts-1" -> 1
func getPodIndexFromName(podName, statefulSetName string) (int, error) {
	if !strings.HasPrefix(podName, statefulSetName+"-") {
		return -1, fmt.Errorf("pod name %s does not match StatefulSet name %s", podName, statefulSetName)
	}

	suffix := strings.TrimPrefix(podName, statefulSetName+"-")
	index, err := strconv.Atoi(suffix)
	if err != nil {
		return -1, fmt.Errorf("failed to parse pod index from name %s: %w", podName, err)
	}

	return index, nil
}

// isClusterReady checks if a cluster is ready based on its status
func isClusterReady(status *clusterv1alpha1.ClusterStatus) bool {
	for _, condition := range status.Conditions {
		if condition.Type == clusterv1alpha1.ClusterConditionReady {
			return condition.Status == metav1.ConditionTrue
		}
	}
	return false
}

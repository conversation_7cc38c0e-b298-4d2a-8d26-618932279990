// Code generated by informer-gen. DO NOT EDIT.

package v1alpha2

import (
	internalinterfaces "github.com/karmada-io/karmada/pkg/generated/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// ClusterResourceBindings returns a ClusterResourceBindingInformer.
	ClusterResourceBindings() ClusterResourceBindingInformer
	// ResourceBindings returns a ResourceBindingInformer.
	ResourceBindings() ResourceBindingInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// ClusterResourceBindings returns a ClusterResourceBindingInformer.
func (v *version) ClusterResourceBindings() ClusterResourceBindingInformer {
	return &clusterResourceBindingInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// ResourceBindings returns a ResourceBindingInformer.
func (v *version) ResourceBindings() ResourceBindingInformer {
	return &resourceBindingInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

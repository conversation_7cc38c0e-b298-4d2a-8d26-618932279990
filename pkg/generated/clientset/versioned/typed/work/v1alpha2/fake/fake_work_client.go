// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha2 "github.com/karmada-io/karmada/pkg/generated/clientset/versioned/typed/work/v1alpha2"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeWorkV1alpha2 struct {
	*testing.Fake
}

func (c *FakeWorkV1alpha2) ClusterResourceBindings() v1alpha2.ClusterResourceBindingInterface {
	return &FakeClusterResourceBindings{c}
}

func (c *FakeWorkV1alpha2) ResourceBindings(namespace string) v1alpha2.ResourceBindingInterface {
	return &FakeResourceBindings{c, namespace}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeWorkV1alpha2) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}

// Code generated by client-gen. DO NOT EDIT.

package v1alpha2

import (
	"net/http"

	v1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/generated/clientset/versioned/scheme"
	rest "k8s.io/client-go/rest"
)

type WorkV1alpha2Interface interface {
	RESTClient() rest.Interface
	ClusterResourceBindingsGetter
	ResourceBindingsGetter
}

// WorkV1alpha2Client is used to interact with features provided by the work.karmada.io group.
type WorkV1alpha2Client struct {
	restClient rest.Interface
}

func (c *WorkV1alpha2Client) ClusterResourceBindings() ClusterResourceBindingInterface {
	return newClusterResourceBindings(c)
}

func (c *WorkV1alpha2Client) ResourceBindings(namespace string) ResourceBindingInterface {
	return newResourceBindings(c, namespace)
}

// NewForConfig creates a new WorkV1alpha2Client for the given config.
// NewForConfig is equivalent to NewForConfigAndClient(c, httpClient),
// where httpClient was generated with rest.HTTPClientFor(c).
func NewForConfig(c *rest.Config) (*WorkV1alpha2Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	httpClient, err := rest.HTTPClientFor(&config)
	if err != nil {
		return nil, err
	}
	return NewForConfigAndClient(&config, httpClient)
}

// NewForConfigAndClient creates a new WorkV1alpha2Client for the given config and http client.
// Note the http client provided takes precedence over the configured transport values.
func NewForConfigAndClient(c *rest.Config, h *http.Client) (*WorkV1alpha2Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientForConfigAndClient(&config, h)
	if err != nil {
		return nil, err
	}
	return &WorkV1alpha2Client{client}, nil
}

// NewForConfigOrDie creates a new WorkV1alpha2Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *WorkV1alpha2Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new WorkV1alpha2Client for the given RESTClient.
func New(c rest.Interface) *WorkV1alpha2Client {
	return &WorkV1alpha2Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1alpha2.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *WorkV1alpha2Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
